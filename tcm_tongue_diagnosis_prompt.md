# 中医AI舌诊可视化网页设计提示词

## 项目背景
创建一个中医AI舌诊技术的专业网页，融合传统中医理论与现代AI技术，在平板上使用，只要做一个单页就行，不需要上下活动

## 视觉设计要求

### 配色方案
- **主色调**：科技感背景AI配色，可以是科技蓝渐变白等
- **高亮色**：中医金色 (#FFD700) 和朱砂红 (#FF4444)
- **辅助色**：温润玉白 (#F8F8F8) 和淡青色 (#E8F4F8)

### 布局风格
- 采用Bento Grid响应式布局
- 超大数字展示诊断准确率（如 95.8%）
- 舌诊图像作为核心视觉元素，占据显著位置
- 中英文混排：中文标题使用粗体大字，英文副标题作为点缀

### 内容模块设计

#### 1. 英雄区域
- 超大标题："AI舌诊" + "Traditional Chinese Medicine Meets AI"
- 动态舌头3D模型或高清舌诊图像轮播
- 实时诊断数据可视化

#### 2. 核心功能展示
- **舌质分析**：颜色识别、纹理检测
- **舌苔诊断**：厚薄、润燥、颜色分析
- **体质判断**：九种体质分类可视化
- **健康建议**：个性化调理方案

#### 3. 技术实力
- AI模型准确率统计图表
- 训练数据量展示（如：100万+ 舌诊样本）
- 识别速度：毫秒级响应
- 支持的舌诊特征数量

#### 4. 中医理论融合
- 五行相生相克动态图
- 经络穴位可视化
- 古籍引用与现代解读对比

## 技术实现要求

### 前端技术栈
- **HTML5** + **TailwindCSS 3.0+**（CDN引入）
- **Framer Motion**（CDN引入）实现流畅动画
- **Chart.js** 或 **D3.js** 用于数据可视化
- **Three.js**（可选）用于3D舌头模型

### 动效设计
- 模仿Apple官网的滚动视差效果
- 舌诊图像的悬浮放大效果
- 数据图表的渐进式加载动画
- 页面元素的淡入淡出过渡

### 图标系统
- 使用**Lucide Icons**或**Heroicons**
- 自定义中医相关SVG图标
- 避免使用emoji，保持专业性

### 响应式设计
- 移动端优先的设计理念
- 平板和桌面端的适配优化
- 触摸友好的交互设计

## 内容要点（不可省略）

### 核心卖点
1. **AI技术优势**：深度学习算法、图像识别精度
2. **中医传承**：千年理论基础、现代科技验证
3. **便民服务**：随时随地、即拍即诊
4. **专业可靠**：医师团队支持、权威机构认证

### 数据展示
- 诊断准确率、用户数量、合作医院
- 识别的舌诊特征类型数量
- 平均诊断时间、用户满意度

### 功能演示
- 实时舌诊体验入口
- 诊断报告样例展示
- 健康档案管理界面

## 特殊要求
- 体现中医文化的深厚底蕴
- 突出AI技术的先进性
- 保持医疗产品的专业性和可信度
- 融入适当的中国传统文化元素（如水墨、印章等）